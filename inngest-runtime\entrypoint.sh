#!/bin/bash

# Function to handle shutdown gracefully
cleanup() {
    echo "🛑 Shutting down services..."
    kill $NEXTJS_PID $INNGEST_PID 2>/dev/null
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Test database connectivity
echo "🔍 Testing database connectivity..."
if [ -n "$DATABASE_URL" ]; then
    echo "📊 DATABASE_URL is set: ${DATABASE_URL}"

    # Extract connection details from DATABASE_URL
    DB_HOST=$(echo "$DATABASE_URL" | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo "$DATABASE_URL" | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')

    echo "🌐 Testing connection to $DB_HOST:$DB_PORT..."

    # Test if port is reachable
    if timeout 5 bash -c "</dev/tcp/$DB_HOST/$DB_PORT"; then
        echo "✅ Database port $DB_HOST:$DB_PORT is reachable"
    else
        echo "❌ Cannot reach database at $DB_HOST:$DB_PORT"
        echo "🔧 Trying to resolve host..."
        nslookup "$DB_HOST" || echo "❌ Host resolution failed"
    fi
else
    echo "❌ DATABASE_URL is not set"
fi

# Test AI service connectivity
echo "🤖 Testing AI service connectivity..."
if [ -n "$AI_API_URL" ]; then
    echo "🔗 AI_API_URL is set: ${AI_API_URL}"

    # Extract host and port from AI_API_URL
    AI_HOST=$(echo "$AI_API_URL" | sed -n 's|.*://\([^:]*\):.*|\1|p')
    AI_PORT=$(echo "$AI_API_URL" | sed -n 's|.*:\([0-9]*\).*|\1|p')

    if [ -n "$AI_HOST" ] && [ -n "$AI_PORT" ]; then
        echo "🌐 Testing connection to AI service at $AI_HOST:$AI_PORT..."

        if timeout 5 bash -c "</dev/tcp/$AI_HOST/$AI_PORT"; then
            echo "✅ AI service port $AI_HOST:$AI_PORT is reachable"
        else
            echo "❌ Cannot reach AI service at $AI_HOST:$AI_PORT"
            echo "🔧 Make sure your AI service is running on port $AI_PORT"
        fi
    else
        echo "❌ Could not parse AI service host/port from: $AI_API_URL"
    fi
else
    echo "❌ AI_API_URL is not set"
fi

# Start Next.js development server in the background
echo "🚀 Starting Next.js development server on port 3000..."
cd /app
npm run dev &
NEXTJS_PID=$!

# Wait for Next.js to be ready
echo "⏳ Waiting for Next.js to be ready..."
until curl -sf http://localhost:3000 > /dev/null; do
  sleep 2
done

echo "✅ Next.js is ready on port 3000."

# Start Inngest OSS Runtime in the background with auto-sync
echo "🚀 Starting Inngest Runtime with auto-sync on port 8288..."
# Use --sdk-url flag to automatically sync the function server
inngest start --event-key "$INNGEST_EVENT_KEY" --signing-key "$INNGEST_SIGNING_KEY" --sdk-url "http://localhost:3000/api/inngest" &
INNGEST_PID=$!

# Wait until the Inngest runtime is healthy
echo "⏳ Waiting for Inngest to be ready..."
until curl -sf http://localhost:8288/healthz > /dev/null; do
  sleep 1
done

echo "✅ Inngest is ready on port 8288."

# Function server should auto-sync via --sdk-url flag
echo "📡 Function server configured for auto-sync"
echo "🔗 Function server URL: http://localhost:3000/api/inngest"
echo "🌐 Next.js app available at: http://localhost:3000"
echo "🌐 Inngest UI available at: http://localhost:8288"
echo "📱 Apps page: http://localhost:8288/apps"
echo ""
echo "✅ Both services are running in the same container!"
echo "✅ If auto-sync doesn't work, you can manually sync via the UI"

# Keep container alive and wait for both processes
wait $NEXTJS_PID $INNGEST_PID
