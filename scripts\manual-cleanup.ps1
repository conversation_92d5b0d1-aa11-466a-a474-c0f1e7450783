# Manual Redis Cleanup Script for Windows
# Run this script to manually clean up Redis data

param(
    [int]$Days = 30,
    [switch]$DryRun = $false,
    [string]$ContainerName = "inngest_redis"
)

Write-Host "🧹 Manual Redis Cleanup Script" -ForegroundColor Green
Write-Host "📅 Cleaning data older than $Days days" -ForegroundColor Yellow

if ($DryRun) {
    Write-Host "🔍 DRY RUN MODE - No data will be deleted" -ForegroundColor Cyan
}

# Check if Docker is running
try {
    docker ps | Out-Null
} catch {
    Write-Host "❌ Docker is not running or not accessible" -ForegroundColor Red
    exit 1
}

# Check if Redis container exists
$containerExists = docker ps -a --filter "name=$ContainerName" --format "{{.Names}}" | Select-String $ContainerName
if (-not $containerExists) {
    Write-Host "❌ Redis container '$ContainerName' not found" -ForegroundColor Red
    Write-Host "Available containers:" -ForegroundColor Yellow
    docker ps -a --format "table {{.Names}}\t{{.Status}}"
    exit 1
}

# Check if container is running
$containerRunning = docker ps --filter "name=$ContainerName" --format "{{.Names}}" | Select-String $ContainerName
if (-not $containerRunning) {
    Write-Host "❌ Redis container '$ContainerName' is not running" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found running Redis container: $ContainerName" -ForegroundColor Green

# Get current Redis info
Write-Host "📊 Current Redis info:" -ForegroundColor Blue
docker exec $ContainerName redis-cli INFO memory | Select-String "used_memory_human"

# Count keys before cleanup
$keysBefore = docker exec $ContainerName redis-cli DBSIZE
Write-Host "🔢 Keys before cleanup: $keysBefore" -ForegroundColor Blue

# Define cleanup patterns
$patterns = @(
    "inngest:runs:*",
    "inngest:events:*", 
    "inngest:history:*",
    "inngest:logs:*",
    "inngest:queue:*",
    "inngest:cache:*"
)

$totalDeleted = 0

foreach ($pattern in $patterns) {
    Write-Host "🔍 Processing pattern: $pattern" -ForegroundColor Yellow
    
    # Get keys matching pattern
    $keys = docker exec $ContainerName redis-cli KEYS $pattern
    
    if (-not $keys -or $keys -eq "(empty array)") {
        Write-Host "   ✅ No keys found for pattern $pattern" -ForegroundColor Green
        continue
    }
    
    $deletedCount = 0
    
    # Convert keys to array if it's a single string
    if ($keys -is [string]) {
        $keys = @($keys)
    }
    
    foreach ($key in $keys) {
        if ([string]::IsNullOrWhiteSpace($key)) {
            continue
        }
        
        # For cache and log patterns, delete without timestamp check
        if ($pattern -like "*cache*" -or $pattern -like "*logs*") {
            if (-not $DryRun) {
                docker exec $ContainerName redis-cli DEL $key | Out-Null
                Write-Host "   🗑️  Deleted: $key" -ForegroundColor Red
            } else {
                Write-Host "   🔍 Would delete: $key" -ForegroundColor Cyan
            }
            $deletedCount++
        }
        # For other patterns, you could add timestamp-based logic here
        elseif ($key -match '\d{10}') {
            # Extract timestamp and compare (simplified version)
            $timestamp = [regex]::Match($key, '\d{10}').Value
            $keyDate = [DateTimeOffset]::FromUnixTimeSeconds([long]$timestamp).DateTime
            $cutoffDate = (Get-Date).AddDays(-$Days)
            
            if ($keyDate -lt $cutoffDate) {
                if (-not $DryRun) {
                    docker exec $ContainerName redis-cli DEL $key | Out-Null
                    Write-Host "   🗑️  Deleted old key: $key" -ForegroundColor Red
                } else {
                    Write-Host "   🔍 Would delete: $key" -ForegroundColor Cyan
                }
                $deletedCount++
            }
        }
    }
    
    Write-Host "   📊 Pattern $pattern`: $deletedCount keys processed" -ForegroundColor Blue
    $totalDeleted += $deletedCount
}

# Set TTL on remaining keys
Write-Host "⏰ Setting TTL on remaining keys..." -ForegroundColor Yellow
$ttlSeconds = $Days * 24 * 60 * 60

foreach ($pattern in $patterns) {
    $keys = docker exec $ContainerName redis-cli KEYS $pattern
    
    if ($keys -and $keys -ne "(empty array)") {
        if ($keys -is [string]) {
            $keys = @($keys)
        }
        
        foreach ($key in $keys) {
            if ([string]::IsNullOrWhiteSpace($key)) {
                continue
            }
            
            # Check if key already has TTL
            $currentTtl = docker exec $ContainerName redis-cli TTL $key
            if ($currentTtl -eq "-1") {
                if (-not $DryRun) {
                    docker exec $ContainerName redis-cli EXPIRE $key $ttlSeconds | Out-Null
                    Write-Host "   ⏰ Set ${Days}d TTL on: $key" -ForegroundColor Green
                } else {
                    Write-Host "   🔍 Would set TTL on: $key" -ForegroundColor Cyan
                }
            }
        }
    }
}

# Count keys after cleanup
$keysAfter = docker exec $ContainerName redis-cli DBSIZE
Write-Host "🔢 Keys after cleanup: $keysAfter" -ForegroundColor Blue
Write-Host "🗑️  Total keys processed: $totalDeleted" -ForegroundColor Blue

# Get Redis info after cleanup
Write-Host "📊 Redis info after cleanup:" -ForegroundColor Blue
docker exec $ContainerName redis-cli INFO memory | Select-String "used_memory_human"

# Trigger AOF rewrite
if (-not $DryRun) {
    Write-Host "🔧 Compacting Redis database..." -ForegroundColor Yellow
    docker exec $ContainerName redis-cli BGREWRITEAOF | Out-Null
}

Write-Host "✅ Redis cleanup completed!" -ForegroundColor Green

if ($DryRun) {
    Write-Host "🔍 This was a dry run. To actually delete data, run without -DryRun flag" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Yellow
Write-Host "  .\manual-cleanup.ps1                    # Clean data older than 30 days"
Write-Host "  .\manual-cleanup.ps1 -Days 7            # Clean data older than 7 days"
Write-Host "  .\manual-cleanup.ps1 -DryRun            # Preview what would be deleted"
Write-Host "  .\manual-cleanup.ps1 -ContainerName inngest_redis_prod  # Different container"
