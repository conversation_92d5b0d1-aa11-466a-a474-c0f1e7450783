# Use specific version of inngest image. Using inngest/inngest:latest panics. Try if that works for you, if not use specific version.
FROM inngest/inngest:v1.8.2

# Install Node.js, npm, curl and other dependencies
USER root
RUN apt-get update && apt-get install -y \
    curl \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Set working directory for the Next.js app
WORKDIR /app

# Copy the athenafunctions package files first for better caching
COPY athenafunctions/package*.json ./
COPY athenafunctions/tsconfig.json ./
COPY athenafunctions/next.config.ts ./
COPY athenafunctions/postcss.config.mjs ./
COPY athenafunctions/eslint.config.mjs ./

# Install Node.js dependencies
RUN npm install

# Copy the rest of the Next.js application
COPY athenafunctions/src/ ./src/
COPY athenafunctions/public/ ./public/

# Copy Prisma schema from src directory (needed for generation)
COPY athenafunctions/src/prisma/ ./prisma/

# Set a dummy DATABASE_URL for Prisma generation (not used at build time)
ENV DATABASE_URL="postgresql://dummy:dummy@localhost:5432/dummy"

# Generate Prisma client
RUN npx prisma generate

# Build the Next.js application
RUN npm run build

# Copy entrypoint script
COPY inngest-runtime/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Expose ports for both services
EXPOSE 3000 8288

ENTRYPOINT ["/entrypoint.sh"]
