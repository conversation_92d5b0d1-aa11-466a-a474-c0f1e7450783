import { z } from "zod";

export const LoginSchema = z.object({
  email: z.string().email({
    message: "Email is required.",
  }),
  password: z.string().min(1, {
    message: "Password is required.",
  }),
  code: z.optional(z.string()),
});

export const RegisterSchema = z.object({
  email: z.string().email({
    message: "Email is required.",
  }),
  password: z.string().min(8, {
    message: "Required: Minimum 8 characters.",
  }),
  firstname: z.string().min(2, {
    message: "Required: Minimum 2 characters",
  }),
  lastname: z.string().min(2, {
    message: "Required: Minimum 2 characters",
  }),
});

export const ResetSchema = z.object({
  email: z.string().email({
    message: "Email is required.",
  }),
});

export const NewPasswordSchema = z.object({
  password: z.string().min(8, {
    message: "Minimum 8 characters required.",
  }),
});

export const CompanySchema = z.object({
  id: z.string().optional(),
  name: z.string().min(2, "Company name must be at least 4 characters"),
  location: z.string().min(2, "Please provide your location"),
  address: z.string().optional().or(z.literal("")),
  about: z.string().optional().or(z.literal("")),
  description: z.string().optional().or(z.literal("")),
  email: z.string().optional().or(z.literal("")),
  phone: z.string().optional().or(z.literal("")),    
  logo: z.string().min(1, "Please provide a logo"),
  website: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
  xAccount: z
    .string()
    .min(4, "Please provide your X handle")
    .optional()
    .or(z.literal("")),
  linkedIn: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
  tin: z.string().optional().or(z.literal("")),
  benefits: z.array(z.string()).optional(),
  foreignerRatio: z.number().min(0).max(100).default(0).optional(),
  englishUsageRatio: z.number().min(0).max(100).default(0).optional(),
});

export const ExistingCompanySchema = z.object({
  companyId: z.string().min(1, "You must select an existing company."),
});

export const JobSeekerSchema = z.object({
  firstname: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .optional()
    .or(z.literal("")),
  lastname: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .optional()
    .or(z.literal("")),
  title: z.string().optional(),
  location: z.string().min(2, "Please provide your location"),
  about: z.string().optional().or(z.literal("")),
  resume: z.string().min(1, "Please upload your resume"),
  portfolio: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
  linkedin: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
  github: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
  writing: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
});

export const JobSchema = z.object({
  id: z.string().optional(),
  jobTitle: z.string().min(1, "Job title is required"),
  employmentType: z.string().min(1, "Employment type is required"),
  experienceLevel: z.string().optional(),
  country: z.string().min(1, "Country is required"),
  location: z.string().optional(),
  department: z.string().optional(),
  salaryCurrency: z.string().optional(),
  salaryFrom: z
    .number()
    .min(0, "Minimum salary must be 0 or greater")
    .optional(),
  salaryTo: z.number().min(0, "Maximum salary must be 0 or greater").optional(),
  jobDescription: z.string().min(1, "Job description is required"),
  listingDuration: z.number().min(1, "Listing duration is required"),
  interviewType: z.enum(["ONLINE", "ONSITE", "HYBRID"]),
  localRemoteWork: z.boolean(),
  overseasRemoteWork: z.boolean(),
  skills: z.array(
    z.object({
      category: z.string(),
      name: z.string(),
    })
  ),
  languageRequirements: z.array(
    z.object({
      language: z.enum([
        "TAGALOG",
        "CEBUANO",
        "JAPANESE",
        "ENGLISH",
        "MANDARIN",
        "KOREAN",
        "VIETNAMESE",
      ]),
      level: z.enum(["NATIVE", "FLUENT", "BUSINESS", "CONVERSATIONAL"]),
      certification: z.string().optional(),
      type: z.enum(["REQUIRED", "PREFERRED"]),
    })
  ),
  tags: z.array(z.string()),
  companyId: z.string().optional(),
  status: z.string().optional(),
  createdAt: z.date().optional(),
});

export const UserJobSeekerSchema = z.object({
    id: z.string().optional(),
    userId: z.string().optional(),
    firstname: z
      .string()
      .min(2, "Name must be at least 2 characters")
      .optional()
      .or(z.literal("")),
    lastname: z
      .string()
      .min(2, "Name must be at least 2 characters")
      .optional()
      .or(z.literal("")),
    title: z.string().optional(),
    location: z.string().min(2, "Please provide your location"),
    about: z.string().optional().or(z.literal("")),
    portfolio: z
      .string()
      .url("Please enter a valid URL")
      .optional()
      .or(z.literal("")),
    linkedin: z
      .string()
      .url("Please enter a valid URL")
      .optional()
      .or(z.literal("")),
    github: z
      .string()
      .url("Please enter a valid URL")
      .optional()
      .or(z.literal("")),
    writing: z
      .string()
      .url("Please enter a valid URL")
      .optional()
      .or(z.literal("")),
    mobilePhone: z.string().optional(),
    birthDate: z.string().optional(),
  });

export const JobNoteSchema = z.object({
  id: z.string().optional(),
  userId: z.string().optional(),
  companyId: z.string().optional(),
  jobId: z.string().optional(),
  resumeId: z.string().optional(),
  note: z.string().min(1, "Note is required"),
  overall_score: z.coerce.number().min(0).max(100).default(0),
  skills_match: z.coerce.number().min(0).max(100).default(0),
  experience_alignment: z.coerce.number().min(0).max(100).default(0),
  education_fit: z.coerce.number().min(0).max(100).default(0),
  createdAt: z.date().optional(),
  culturalFitNote: z.string().optional(),
  attitudeWorkEthicNote: z.string().optional(),
  softSkillsNote: z.string().optional(),
  problemSolvingNote: z.string().optional(),
  potentialLearningAgilityNote: z.string().optional(),
  professionalismPresentationNote: z.string().optional(),
  referencesReputationNote: z.string().optional(),
  passionInterestNote: z.string().optional(),
  availabilityFlexibilityNote: z.string().optional(),
  salaryExpectationsNote: z.string().optional(),
  other_notes: z.array(z.object({
    note_type: z.string(),
    content: z.string(),
  })).optional(),
});

// Type inferred from CompanySchema for form data
export type CompanyFormData = z.infer<typeof CompanySchema>;

// A more specific type for company details as fetched from the server.
// We ensure 'id' is present and adjust types for fields that might be null when fetched,
// even if the form schema has different rules (e.g., logo required for creation).
export interface FetchedCompanyDetails {
  id: string; // 'id' is not optional when fetched
  name: string;
  location: string;
  address?: string | null;
  about?: string | null;
  description?: string | null;
  email?: string | null;
  phone?: string | null;
  logo?: string | null; // 'logo' might be null if not set, even if schema requires it for new company
  website?: string | null;
  xAccount?: string | null;
  linkedIn?: string | null;
  tin?: string | null;
  benefits?: string[] | null; // Schema has it optional, fetched could be array or null
  foreignerRatio?: number | null; // Schema has default and optional, fetched could be number or null
  englishUsageRatio?: number | null; // Same as foreignerRatio
  // Include any other fields that GetCompany returns for a company
}

// Type for the overall payload returned by your GetCompany server action
export interface GetCompanyActionPayload {
  success: boolean; // Indicates if the action was successful
  company: FetchedCompanyDetails | null; // The company data, or null if not found/error
  error?: string; // Optional error message
}

// Type inferred from JobSchema for form data
export type JobFormData = z.infer<typeof JobSchema>;

export interface FetchedJobDetails {
  id: string;
  jobTitle: string;
  employmentType: string;
  experienceLevel?: string | null;
  country: string;
  location?: string | null;
  department?: string | null;
  salaryCurrency?: string | null;
  salaryFrom?: number | null;
  salaryTo?: number | null;
  jobDescription: string;
  listingDuration?: number | null;
  interviewType?: string | null;
  localRemoteWork: boolean;
  overseasRemoteWork: boolean;
  skills: string[];
  languageRequirements: string[];
  tags: string[];
  companyId: string | null;
  status: string | null;
  createdAt: Date | null;
}

export interface GetJobActionPayload {
  success: boolean;
  job: FetchedJobDetails | null;
  error?: string;
}

// Type for the raw company stats data returned by getCompanyStatsData
export type CompanyStatsData = {
  totalActiveJobs: number;
  totalApplicants: number;
  totalResumes: number;
  totalMatches: number;
  totalFiles: number;
  totalJobs: number;
  newApplicants: number;
  newShortlist: number;
  companyName: { name: string } | null; 
};

export type GetCompanyStatsActionPayload = {
  success: boolean;
  stats: CompanyStatsData | null;
  error?: string;
};