#!/bin/bash

# Simple scheduler to replace cron and avoid setpgid issues
# Runs cleanup daily at 2 AM

echo "🕐 Redis Cleanup Scheduler started"
echo "📅 Will run cleanup daily at 2:00 AM"

# Function to calculate seconds until next 2 AM
calculate_sleep_time() {
    current_hour=$(date +%H)
    current_minute=$(date +%M)
    current_second=$(date +%S)
    
    # Calculate current time in seconds since midnight
    current_seconds=$((current_hour * 3600 + current_minute * 60 + current_second))
    
    # Target time: 2 AM (2 * 3600 = 7200 seconds)
    target_seconds=7200
    
    # Calculate seconds until next 2 AM
    if [ $current_seconds -lt $target_seconds ]; then
        # 2 AM is today
        sleep_seconds=$((target_seconds - current_seconds))
    else
        # 2 AM is tomorrow (24 hours - current + target)
        sleep_seconds=$((86400 - current_seconds + target_seconds))
    fi
    
    echo $sleep_seconds
}

# Run cleanup immediately on first start (optional)
if [ "${RUN_ON_START:-false}" = "true" ]; then
    echo "🚀 Running initial cleanup..."
    /usr/local/bin/redis-cleanup.sh >> /var/log/redis-cleanup.log 2>&1
fi

# Main scheduler loop
while true; do
    sleep_time=$(calculate_sleep_time)
    next_run=$(date -d "+${sleep_time} seconds" '+%Y-%m-%d %H:%M:%S')
    
    echo "⏰ Next cleanup scheduled for: $next_run (sleeping for ${sleep_time} seconds)"
    
    # Sleep until next 2 AM
    sleep $sleep_time
    
    # Run the cleanup
    echo "🧹 Starting scheduled Redis cleanup at $(date)"
    /usr/local/bin/redis-cleanup.sh >> /var/log/redis-cleanup.log 2>&1
    
    echo "✅ Cleanup completed at $(date)"
done
