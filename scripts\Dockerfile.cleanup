FROM alpine:latest

# Install redis-cli and bash
RUN apk add --no-cache redis bash

# Copy cleanup script and scheduler
COPY redis-cleanup.sh /usr/local/bin/redis-cleanup.sh
COPY scheduler.sh /usr/local/bin/scheduler.sh
RUN chmod +x /usr/local/bin/redis-cleanup.sh /usr/local/bin/scheduler.sh

# Create log file
RUN touch /var/log/redis-cleanup.log

# Use custom scheduler instead of cron to avoid setpgid issues
CMD ["/usr/local/bin/scheduler.sh"]
