FROM alpine:latest

# Install redis-cli and cron
RUN apk add --no-cache redis bash dcron

# Copy cleanup script
COPY redis-cleanup.sh /usr/local/bin/redis-cleanup.sh
RUN chmod +x /usr/local/bin/redis-cleanup.sh

# Create cron job to run cleanup every day at 2 AM
RUN echo "0 2 * * * /usr/local/bin/redis-cleanup.sh >> /var/log/redis-cleanup.log 2>&1" > /etc/crontabs/root

# Create log file
RUN touch /var/log/redis-cleanup.log

# Start cron daemon with -d flag to avoid setpgid issues
CMD ["crond", "-f", "-d", "0"]
