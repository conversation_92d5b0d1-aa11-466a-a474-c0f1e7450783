import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Basic health check
    const health: any = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'athenafunctions',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    };

    // Check if Inngest client is accessible
    try {
      // Simple check - if we can import the client, it's configured
      const { inngest } = await import('@/inngest/client');
      health.inngest = {
        status: 'configured',
        clientId: inngest.id,
      };
    } catch (error) {
      health.inngest = {
        status: 'error',
        error: 'Failed to load Inngest client',
      };
    }

    return NextResponse.json(health, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
