version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: inngest_redis_prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  inngest:
    build:
      context: .
      dockerfile: ./inngest-runtime/Dockerfile
    container_name: inngest_athenafunctions_prod
    restart: unless-stopped
    ports:
      - "3000:3000"  # Next.js app (athenafunctions)
      - "8288:8288"  # Inngest runtime
    depends_on:
      redis:
        condition: service_healthy
    environment:
      REDIS_URL: redis://redis:6379
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      # Production optimizations
      INNGEST_LOG_LEVEL: info
      INNGEST_CONCURRENCY: 10
    env_file:
      - .env.production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - app_logs:/app/logs

  redis-cleanup:
    build:
      context: ./scripts
      dockerfile: Dockerfile.cleanup
    container_name: inngest_redis_cleanup_prod
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CLEANUP_DAYS: 30
      DRY_RUN: false
    volumes:
      - cleanup_logs:/var/log
    healthcheck:
      test: ["CMD", "test", "-f", "/var/log/redis-cleanup.log"]
      interval: 24h
      timeout: 10s
      retries: 1

volumes:
  redis_data:
    driver: local
  app_logs:
    driver: local
  cleanup_logs:
    driver: local
