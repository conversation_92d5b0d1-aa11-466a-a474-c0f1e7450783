#!/bin/bash

# Redis Cleanup Script
# Automatically removes old data to prevent database growth

set -e

# Configuration
REDIS_HOST=${REDIS_HOST:-"localhost"}
REDIS_PORT=${REDIS_PORT:-"6379"}
REDIS_DB=${REDIS_DB:-"0"}
CLEANUP_DAYS=${CLEANUP_DAYS:-"30"}
DRY_RUN=${DRY_RUN:-"false"}

# Calculate timestamp for 30 days ago (in seconds)
CLEANUP_TIMESTAMP=$(date -d "${CLEANUP_DAYS} days ago" +%s)

echo "🧹 Redis Cleanup Script Starting..."
echo "📅 Removing data older than ${CLEANUP_DAYS} days (before $(date -d "${CLEANUP_DAYS} days ago"))"
echo "🔗 Connecting to Redis at ${REDIS_HOST}:${REDIS_PORT}"

if [ "$DRY_RUN" = "true" ]; then
    echo "🔍 DRY RUN MODE - No data will be deleted"
fi

# Function to execute Redis commands
redis_cmd() {
    if command -v redis-cli &> /dev/null; then
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -n "$REDIS_DB" "$@"
    else
        # If running in Docker, use docker exec
        docker exec inngest_redis redis-cli -n "$REDIS_DB" "$@"
    fi
}

# Get Redis info
echo "📊 Current Redis info:"
redis_cmd INFO memory | grep used_memory_human || echo "Could not get memory info"

# Count keys before cleanup
KEYS_BEFORE=$(redis_cmd DBSIZE)
echo "🔢 Keys before cleanup: $KEYS_BEFORE"

# Cleanup Inngest-specific patterns
echo "🧹 Cleaning up old Inngest data..."

# Common Inngest key patterns to clean up
PATTERNS=(
    "inngest:runs:*"           # Function run data
    "inngest:events:*"         # Event data
    "inngest:history:*"        # Execution history
    "inngest:logs:*"           # Log entries
    "inngest:queue:*"          # Old queue entries
    "inngest:cache:*"          # Cached data
)

TOTAL_DELETED=0

for pattern in "${PATTERNS[@]}"; do
    echo "🔍 Processing pattern: $pattern"
    
    # Get all keys matching the pattern
    KEYS=$(redis_cmd KEYS "$pattern" 2>/dev/null || echo "")
    
    if [ -z "$KEYS" ]; then
        echo "   ✅ No keys found for pattern $pattern"
        continue
    fi
    
    DELETED_COUNT=0
    
    # Process each key
    while IFS= read -r key; do
        if [ -z "$key" ]; then
            continue
        fi
        
        # Check if key has a timestamp or TTL
        KEY_TTL=$(redis_cmd TTL "$key" 2>/dev/null || echo "-1")
        
        # If key has no TTL (-1) or is expired (-2), check if it's old
        if [ "$KEY_TTL" = "-1" ] || [ "$KEY_TTL" = "-2" ]; then
            # For keys without TTL, check if they contain timestamp
            # This is a heuristic - adjust based on your Inngest key structure
            if [[ "$key" =~ [0-9]{10} ]]; then
                # Extract timestamp from key (assuming Unix timestamp in key)
                KEY_TIMESTAMP=$(echo "$key" | grep -o '[0-9]\{10\}' | head -1)
                
                if [ -n "$KEY_TIMESTAMP" ] && [ "$KEY_TIMESTAMP" -lt "$CLEANUP_TIMESTAMP" ]; then
                    if [ "$DRY_RUN" = "false" ]; then
                        redis_cmd DEL "$key" > /dev/null
                        echo "   🗑️  Deleted old key: $key"
                    else
                        echo "   🔍 Would delete: $key"
                    fi
                    ((DELETED_COUNT++))
                fi
            else
                # For keys without timestamp, delete if they're in cleanup patterns
                # and older than a certain threshold (be careful here)
                if [[ "$pattern" == "inngest:cache:*" ]] || [[ "$pattern" == "inngest:logs:*" ]]; then
                    if [ "$DRY_RUN" = "false" ]; then
                        redis_cmd DEL "$key" > /dev/null
                        echo "   🗑️  Deleted cache/log key: $key"
                    else
                        echo "   🔍 Would delete: $key"
                    fi
                    ((DELETED_COUNT++))
                fi
            fi
        fi
    done <<< "$KEYS"
    
    echo "   📊 Pattern $pattern: $DELETED_COUNT keys processed"
    TOTAL_DELETED=$((TOTAL_DELETED + DELETED_COUNT))
done

# Set TTL on remaining keys to auto-expire
echo "⏰ Setting TTL on remaining keys..."
TTL_SECONDS=$((CLEANUP_DAYS * 24 * 60 * 60))  # Convert days to seconds

for pattern in "${PATTERNS[@]}"; do
    KEYS=$(redis_cmd KEYS "$pattern" 2>/dev/null || echo "")
    
    while IFS= read -r key; do
        if [ -z "$key" ]; then
            continue
        fi
        
        # Only set TTL if key doesn't already have one
        KEY_TTL=$(redis_cmd TTL "$key" 2>/dev/null || echo "-1")
        if [ "$KEY_TTL" = "-1" ]; then
            if [ "$DRY_RUN" = "false" ]; then
                redis_cmd EXPIRE "$key" "$TTL_SECONDS" > /dev/null
                echo "   ⏰ Set ${CLEANUP_DAYS}d TTL on: $key"
            else
                echo "   🔍 Would set TTL on: $key"
            fi
        fi
    done <<< "$KEYS"
done

# Count keys after cleanup
KEYS_AFTER=$(redis_cmd DBSIZE)
echo "🔢 Keys after cleanup: $KEYS_AFTER"
echo "🗑️  Total keys processed: $TOTAL_DELETED"

# Get Redis info after cleanup
echo "📊 Redis info after cleanup:"
redis_cmd INFO memory | grep used_memory_human || echo "Could not get memory info"

# Compact Redis database
if [ "$DRY_RUN" = "false" ]; then
    echo "🔧 Compacting Redis database..."
    redis_cmd BGREWRITEAOF > /dev/null || echo "Could not trigger AOF rewrite"
fi

echo "✅ Redis cleanup completed!"

if [ "$DRY_RUN" = "true" ]; then
    echo "🔍 This was a dry run. To actually delete data, run with DRY_RUN=false"
fi
