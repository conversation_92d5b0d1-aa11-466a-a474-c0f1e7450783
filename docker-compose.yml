version: "3.8"

services:
  redis:
    image: redis:7
    container_name: inngest_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --appendfsync everysec

  inngest:
    build:
      context: .
      dockerfile: ./inngest-runtime/Dockerfile
      args:
        - DATABASE_URL=${DATABASE_URL}
    container_name: inngest_athenafunctions_runtime
    ports:
      - "3000:3000"  # Next.js app (athenafunctions)
      - "8288:8288"  # Inngest runtime
    depends_on:
      - redis
    environment:
      REDIS_URL: redis://redis:6379
      DEBUG: "*"
      # Next.js environment variables
      NODE_ENV: development
      # Explicitly set DATABASE_URL to ensure it uses the correct host
      DATABASE_URL: ${DATABASE_URL}
    env_file:
      - .env
    extra_hosts:
      - "host.docker.internal:host-gateway"

  redis-cleanup:
    build:
      context: ./scripts
      dockerfile: Dockerfile.cleanup
    container_name: inngest_redis_cleanup
    depends_on:
      - redis
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CLEANUP_DAYS: 30
      DRY_RUN: false
    volumes:
      - cleanup_logs:/var/log
    restart: unless-stopped
    cap_add:
      - SYS_ADMIN

volumes:
  redis_data:
    driver: local
  cleanup_logs:
    driver: local
      
