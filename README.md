# Inngest + Next.js Integration (AthenaFunctions)

This project integrates a Next.js 15 application with Inngest functions in a single Docker container setup.

## Architecture

- **Next.js App (athenafunctions)**: Runs on port 3000, serves the web interface and Inngest functions via `/api/inngest`
- **Inngest Runtime**: Runs on port 8288, provides the Inngest UI and executes functions
- **Redis**: Runs on port 6379, used by Inngest for state management

## Features

- 🚀 Next.js 15 with App Router and React 19
- 🎨 Tailwind CSS 4 for styling
- ⚡ Turbopack for fast development
- 🔄 Inngest functions with TypeScript
- 🐳 Single Docker container setup
- 🔗 Auto-sync between Next.js and Inngest
- 🎯 API endpoint for triggering events

## Quick Start

1. **Setup environment**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Inngest keys:
   ```
   INNGEST_EVENT_KEY=your-event-key-here
   INNGEST_SIGNING_KEY=your-signing-key-here
   ```

2. **Start the integrated container**:
   ```bash
   docker-compose up --build
   ```

3. **Access the applications**:
   - **Next.js App**: http://localhost:3000
   - **Inngest UI**: http://localhost:8288
   - **Inngest Apps**: http://localhost:8288/apps

## Available Inngest Functions

The project includes a sample function:

- **hello-world**: Triggered by `test/hello.world` events
  - Takes an email parameter
  - Waits 1 second
  - Returns a greeting message

## Testing Functions

### Using the API

Trigger events using the `/api/trigger` endpoint:

```bash
curl -X POST http://localhost:3000/api/trigger \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "test/hello.world",
    "data": {
      "email": "<EMAIL>"
    }
  }'
```

### Using the Inngest UI

1. Open http://localhost:8288
2. Navigate to the Apps page
3. View your registered functions
4. Trigger events manually

## Project Structure

```
├── athenafunctions/           # Next.js 15 application
│   ├── src/
│   │   ├── app/
│   │   │   ├── api/
│   │   │   │   ├── inngest/   # Inngest function endpoint
│   │   │   │   └── trigger/   # Event trigger API
│   │   │   ├── layout.tsx
│   │   │   └── page.tsx
│   │   └── inngest/
│   │       ├── client.ts      # Inngest client setup
│   │       └── functions.ts   # Function definitions
│   ├── package.json
│   └── ...
├── inngest-runtime/
│   ├── Dockerfile             # Multi-service container
│   └── entrypoint.sh          # Startup script
├── docker-compose.yml         # Service orchestration
└── .env.example               # Environment template
```

## Development

### Local Development (without Docker)

1. **Start Next.js**:
   ```bash
   cd athenafunctions
   npm install
   npm run dev
   ```

2. **Start Inngest** (in another terminal):
   ```bash
   npx inngest-cli@latest dev
   ```

### Docker Development

The Docker setup automatically:
- Installs Node.js dependencies
- Builds the Next.js application
- Starts both services
- Configures auto-sync between services

## Environment Variables

- `INNGEST_EVENT_KEY`: Your Inngest event key
- `INNGEST_SIGNING_KEY`: Your Inngest signing key
- `REDIS_URL`: Redis connection URL (auto-configured in Docker)
- `NODE_ENV`: Node.js environment (development/production)

## Adding New Functions

1. Create your function in `athenafunctions/src/inngest/functions.ts`
2. Export it in `athenafunctions/src/app/api/inngest/route.ts`
3. Restart the container to sync changes

Example function:
```typescript
export const myFunction = inngest.createFunction(
  { id: "my-function" },
  { event: "my/event" },
  async ({ event, step }) => {
    // Your function logic here
    return { success: true };
  }
);
```

## Production Deployment

### Production Setup

1. **Create production environment**:
   ```bash
   cp .env.production.example .env.production
   ```

   Edit `.env.production` with your production Inngest keys.

2. **Deploy with persistent storage**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d --build
   ```

### Production Features

- **Persistent Redis storage** - Data survives container restarts
- **Health checks** - Automatic service monitoring
- **Restart policies** - Auto-restart on failure
- **Production Redis config** - Optimized for performance and durability
- **Logging** - Structured logs for monitoring

### Production URLs

- **Application**: http://your-domain:3000
- **Inngest UI**: http://your-domain:8288
- **Health Check**: http://your-domain:3000/api/health

### Data Persistence

Production setup includes:
- **Redis data volume** - Function execution history, event logs
- **Application logs volume** - Next.js and Inngest logs
- **AOF persistence** - Redis append-only file for durability

### Data Management

#### Automatic Cleanup (Every 30 Days)

The setup includes automatic Redis cleanup to prevent database growth:

- **Automatic cleanup service** runs daily at 2 AM
- **Removes data older than 30 days** from Inngest patterns
- **Sets TTL on remaining keys** for automatic expiration
- **Logs cleanup activity** to `/var/log/redis-cleanup.log`

#### Manual Cleanup

**Windows (PowerShell):**
```powershell
# Preview what would be deleted (dry run)
.\scripts\manual-cleanup.ps1 -DryRun

# Clean data older than 30 days
.\scripts\manual-cleanup.ps1

# Clean data older than 7 days
.\scripts\manual-cleanup.ps1 -Days 7

# Clean specific container
.\scripts\manual-cleanup.ps1 -ContainerName inngest_redis_prod
```

**Linux/Mac:**
```bash
# Run cleanup script in container
docker exec inngest_redis_cleanup /usr/local/bin/redis-cleanup.sh

# Manual cleanup with different settings
docker run --rm --network inngestapp_default \
  -e REDIS_HOST=redis -e CLEANUP_DAYS=7 -e DRY_RUN=true \
  inngestapp_redis-cleanup /usr/local/bin/redis-cleanup.sh
```

#### View Cleanup Logs

```bash
# View cleanup logs
docker exec inngest_redis_cleanup cat /var/log/redis-cleanup.log

# Follow cleanup logs in real-time
docker exec inngest_redis_cleanup tail -f /var/log/redis-cleanup.log
```

#### Backup & Recovery

```bash
# Backup Redis data
docker run --rm -v inngestapp_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis-backup.tar.gz -C /data .

# Restore Redis data
docker run --rm -v inngestapp_redis_data:/data -v $(pwd):/backup alpine tar xzf /backup/redis-backup.tar.gz -C /data
```

## Troubleshooting

- **Functions not appearing**: Check the Inngest UI at http://localhost:8288/apps
- **Auto-sync issues**: Manually sync via the Inngest UI
- **Port conflicts**: Ensure ports 3000, 6379, and 8288 are available
- **Container issues**: Check logs with `docker-compose logs inngest`
- **Build issues**: Ensure all dependencies are properly installed
- **Production health**: Check `/api/health` endpoint for service status

## Tech Stack

- **Next.js 15**: React framework with App Router
- **React 19**: Latest React version
- **TypeScript**: Type safety
- **Tailwind CSS 4**: Utility-first CSS framework
- **Inngest**: Reliable background functions
- **Docker**: Containerization
- **Redis**: State management for Inngest
