import { GetAllResumeRawText } from "@/actions/resume/userResumeActions";
import prisma from "../lib/prisma/prisma";
import { inngest } from "./client";


const COMPREHENSIVE_PERIODIC_MATCHING_FLAG = "comprehensive_periodic_matching_in_progress";
const AI_API_URL = process.env.AI_API_URL || "http://localhost:8000"; 

export const helloWorld = inngest.createFunction(
  { id: "hello-world" },
  { event: "test/hello.world" },
  async ({ event, step }) => {
    await step.sleep("wait-a-moment", "1s");
    return { message: `Hello ${event.data.email}!` };
  },
);

export const jobToResumePeriodicMatching = inngest.createFunction(
  {
    id: "job-resume-periodic-matching",
    retries: 0, // Disable retries
  },
  { event: "job/resume-matching" },

  async ({ step }) => {
    try {
      const JOB_PROCESSING_BATCH_SIZE = 5; // How many globally unmatched jobs to process in one run
      const FALLBACK_JOB_PROCESSING_BATCH_SIZE = 2; // Smaller batch for fallback processing
      const RESUME_BATCH_SIZE_FOR_AI = 50; // How many resumes to send per AI call for a single job
      let processedFallbackBatch = false;

      let jobsToProcess = await step.run(
        "fetch-unmatched-jobs-batch",
        async () => {
          const jobsWithMatches = await prisma.jobResumeMatch.findMany({
            select: { jobId: true },
            distinct: ["jobId"],
          });
          const matchedJobIds = jobsWithMatches.map(
            (match) => match.jobId
          );

          return await prisma.jobPost.findMany({
            where: {
              id: { notIn: matchedJobIds },
              status: "ACTIVE",
            },
            select: {
              id: true,
              rawText: true,
              jobDescription: true,
              userId: true,
            },
            take: JOB_PROCESSING_BATCH_SIZE,
          });
        }
      );

      // Tier 2: Fallback if no globally unmatched jobs found
      if (!jobsToProcess || jobsToProcess.length === 0) {
        jobsToProcess = await step.run(
          "fetch-fallback-active-jobs-batch",
          async () => {
            // Fetch oldest active jobs that haven't been processed by fallback recently
            return await prisma.jobPost.findMany({
              where: {
                status: "ACTIVE",
                // Optional: Add a condition to re-process after a certain time if needed
                // OR: [
                //   { lastProcessedForFallbackMatchingAt: null },
                //   { lastProcessedForFallbackMatchingAt: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } } // e.g., older than 1 day
                // ]
              },
              select: {
                id: true,
                rawText: true,
                jobDescription: true,
                userId: true,
              },
              orderBy: [
                // Prioritize jobs not yet processed in fallback, then by oldest
                {
                  lastProcessedForFallbackMatchingAt: {
                    sort: "asc",
                    nulls: "first",
                  },
                },
                { createdAt: "asc" }, // Secondary sort for those with null or same timestamp
              ],
              take: FALLBACK_JOB_PROCESSING_BATCH_SIZE,
            });
          }
        );
        if (jobsToProcess && jobsToProcess.length > 0) {
          processedFallbackBatch = true;
        }
      }

      if (!jobsToProcess || jobsToProcess.length === 0) {
        // No work found in either tier
        await step.run(
          "clear-comprehensive-matching-flag-no-job-work",
          async () => {
            await prisma.systemFlag.update({
              where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
              data: { value: "false" },
            });
          }
        );
        return {
          message:
            "No globally unmatched jobs and no fallback active jobs found to process.",
        };
      }

      const allResumesResult = await step.run(
        "fetch-all-resumes-with-content",
        async () => {
          return await GetAllResumeRawText();
        }
      );
      const allResumesWithContent = allResumesResult?.data || [];

      for (const job of jobsToProcess) {
        await step.run(`match-job-${job.id}-to-resumes`, async () => {
          const jobDataForAI = {
            id: job.id,
            raw_text: job.rawText || job.jobDescription || "",
          };

          if (!jobDataForAI.raw_text) {
            console.warn(`Skipping job ${job.id} due to no raw_text.`);
            return;
          }

          if (allResumesWithContent.length === 0) {
            return;
          }

          const existingMatchesForThisJob =
            await prisma.jobResumeMatch.findMany({
              where: { jobId: job.id as string },
              select: { resumeId: true },
            });
          const matchedResumeIdsForThisJob = existingMatchesForThisJob.map(
            (m) => m.resumeId
          );

          const resumesToConsiderForThisJob = allResumesWithContent
            .filter(
              (resume) => !matchedResumeIdsForThisJob.includes(resume.id) // raw_text is already guaranteed by GetAllResumeRawTextData
            )
            .map((resume) => ({
              // Format for AI
              id: resume.id,
              raw_text: resume.raw_text, // Directly use the raw_text from GetAllResumeRawTextData
            }));

          const options = {
            skills_match: 0.4,
            experience_alignment: 0.35,
            education_fit: 0.25,
          };

          for (
            let i = 0;
            i < resumesToConsiderForThisJob.length;
            i += RESUME_BATCH_SIZE_FOR_AI
          ) {
            const resumeChunk = resumesToConsiderForThisJob.slice(
              i,
              i + RESUME_BATCH_SIZE_FOR_AI
            );

            console.log(`🔗 Attempting to call AI service at: ${AI_API_URL}/match/job-to-resumes`);
            console.log(`📊 Sending ${resumeChunk.length} resumes for job ${job.id}`);

            try {
              const response = await fetch(`${AI_API_URL}/match/job-to-resumes`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                  userId: job.userId, // Job poster's ID
                  jobId: job.id,
                  job: jobDataForAI,
                  resumes: resumeChunk,
                  options: options,
                }),
              });

              if (!response.ok) {
                console.error(`❌ AI service responded with status: ${response.status} ${response.statusText}`);
                const errorText = await response.text();
                console.error(`❌ Error response: ${errorText}`);
              } else {
                console.log(`✅ Successfully called AI service for job ${job.id}, chunk ${Math.floor(i / RESUME_BATCH_SIZE_FOR_AI) + 1}`);
              }
            } catch (error) {
              console.error(`❌ Network error calling AI service:`, error);
              console.error(`🔧 Check if AI service is running at ${AI_API_URL}`);

              if (error && typeof error === 'object' && 'code' in error) {
                const errorCode = (error as any).code;
                if (errorCode === 'ENOTFOUND') {
                  console.error('🔧 Host not found. Check if host.docker.internal is resolving correctly.');
                } else if (errorCode === 'ECONNREFUSED') {
                  console.error('🔧 Connection refused. Check if the AI service is running on port 8000.');
                } else if (errorCode === 'ETIMEDOUT') {
                  console.error('🔧 Connection timeout. Check network connectivity.');
                }
              }
            }
            // AI service handles saving results. Log AI response if needed for debugging.
          }
        }); // End of step.run(`match-job-${job.id}-to-resumes`)

        // If this job was processed as part of a fallback batch, update its timestamp.
        // This is done regardless of whether AI calls were made for it in this run.
        if (processedFallbackBatch) {
          await step.run(
            `update-fallback-timestamp-job-${job.id}`,
            async () => {
              await prisma.jobPost.update({
                where: { id: job.id },
                data: { lastProcessedForFallbackMatchingAt: new Date() },
              });
            }
          );
        }
      }

      // Recursion logic adjustment
      if (!processedFallbackBatch) {
        const moreGloballyUnmatchedJobsExist = await step.run(
          "check-if-more-globally-unmatched-jobs-exist",
          async () => {
            const jobsWithMatches = await prisma.jobResumeMatch.findMany({
              select: { jobId: true },
              distinct: ["jobId"],
            });
            const matchedJobIds = jobsWithMatches.map(
              (match) => match.jobId
            );
            const count = await prisma.jobPost.count({
              where: {
                id: { notIn: matchedJobIds },
                status: "ACTIVE",
              },
            });
            return count > 0;
          }
        );

        if (moreGloballyUnmatchedJobsExist) {
          await step.sleep("batch-cooldown", "5m");
          await step.run(
            "trigger-next-batch-for-globally-unmatched-jobs",
            async () => {
              await inngest.send({
                name: "job/resume-matching", // Triggers this function again
                data: {},
              });
            }
          );
          // Return here to prevent clearing the flag if we are recursing for globally unmatched
          return {
            message: "Continuing with next batch of globally unmatched jobs.",
          };
        }
      }

      // If we processed a fallback batch, or if no more globally unmatched jobs exist, clear the flag.
      // `consolidatedCronJobs` will re-trigger if needed for further fallback batches.
      await step.run(
        "clear-comprehensive-matching-flag-job-completed-or-fallback",
        async () => {
          await prisma.systemFlag.update({
            where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
            data: { value: "false" },
          });
        }
      );

      return {
        message: `Completed batch of job-to-resume matching. Processed fallback: ${processedFallbackBatch}`,
      };
    } catch (error) {
      // If there's an error, make sure we still clear the flag
      await step.run(
        "clear-comprehensive-matching-flag-job-on-error",
        async () => {
          await prisma.systemFlag.update({
            where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
            data: { value: "false" },
          });
        }
      );

      throw error; // Re-throw the error for Inngest to handle
    }
  }
);