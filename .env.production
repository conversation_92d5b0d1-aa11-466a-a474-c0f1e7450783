# Production Environment Configuration

# Inngest Configuration (REQUIRED)
INNGEST_SIGNING_KEY="eeb0375b307e960672accf1462ba5a4b05a50a585448345723dbdf2c371c3b97"
INNGEST_EVENT_KEY="522049bcf3de13b9c01f6da6ca3f8cb150b62d43907ab2e888a7698cda1a6310"

# Next.js Production Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Inngest Production Settings
INNGEST_LOG_LEVEL=info
INNGEST_CONCURRENCY=10

ARCJET_KEY="ajkey_01jqjsycxgejkbgkz35n3hqca5"
ARCJET_MODE="LIVE"

# Redis Configuration (automatically set by docker-compose)
# REDIS_URL=redis://redis:6379

# Redis Cleanup Configuration
CLEANUP_DAYS=30
DRY_RUN=false

# Optional: Custom Redis settings
# REDIS_MAX_CONNECTIONS=100
# REDIS_TIMEOUT=5000

# Security (if needed)
# INNGEST_WEBHOOK_SECRET=your-webhook-secret
# API_SECRET_KEY=your-api-secret

# Monitoring (if using external services)
# SENTRY_DSN=your-sentry-dsn
# DATADOG_API_KEY=your-datadog-key
