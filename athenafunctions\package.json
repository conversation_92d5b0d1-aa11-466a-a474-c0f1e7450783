{"name": "athenafunctions", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@arcjet/inspect": "^1.0.0-beta.8", "@arcjet/next": "^1.0.0-beta.8", "@prisma/client": "^6.10.1", "@prisma/extension-accelerate": "^2.0.1", "inngest": "^3.39.2", "mammoth": "^1.9.1", "next": "15.3.4", "pdf-parse-new": "^1.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prisma": "^6.10.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}